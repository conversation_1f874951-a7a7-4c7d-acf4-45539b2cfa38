#!/bin/bash

# =============================================================================
# IPVlan L3大规模命名空间隔离脚本 - 完善防火墙策略功能版
# =============================================================================
# 版本: 5.0 (完善防火墙策略功能版)
# 功能: 支持1-10000个命名空间,业务名称联动,支持iptables/nftables
# 特点: 每个命名空间独立运行WireGuard服务,完善的客户机访问控制
# 隔离: 利用命名空间天然隔离,完整多层级NAT支持
# 优化: 使用IP地址生成防火墙规则,客户机业务访问控制
# 作者: System Administrator
# 更新: 2024年完善防火墙策略功能版本
# =============================================================================

# ============= 业务配置区域 =============
# 核心业务参数配置,根据实际部署环境调整

BUSINESS_NAME="vpn"                 # 业务标识名称,用于命名所有相关组件
NUM_NAMESPACES=3                    # 创建的命名空间总数 (1-10000)
SERVICE_INTERFACE="enp6s18"         # 服务网络接口名称
SERVICE_IP="**************"         # 服务IP地址,用于客户端连接和防火墙规则
EXTERNAL_ACCESS_IP="**************" # 外访IP地址,NAT时统一使用此IP访问外网
BASE_PORT=51821                     # WireGuard端口映射起始端口
FIREWALL_ENGINE="nftables"          # 防火墙引擎: iptables 或 nftables
MAX_PARALLEL=30                     # 并发清理的最大进程数,支持到30

# IP地址智能分配配置
IPVLAN_IP="172.16.0"   # IPVlan网络基础IP段,用于计算命名空间IP
IPVLAN_START_OFFSET=10 # IP分配起始偏移量,避免与网关冲突

# ============= 客户机访问控制配置 =============
ACCESS_CONTROL_TEMPLATE="strict" # 访问控制模板: default/strict/custom

# ============= WireGuard模板配置 =============
# 使用固定密钥配置,避免每次重新生成
SERVER_Address="********/24"                                     # WireGuard服务器虚拟IP段
SERVER_ListenPort="51820"                                        # WireGuard服务器监听端口
SERVER_PrivateKey="wF0XPPhtXqZI+hiGXZFf7k5UDm+tQFH7lvkiULFJPUE=" # 服务器私钥(固定)
CLIENT_PublicKey="I1bMCPSExWgu2riRKLkQaJxFYxJZbPHJCN4KI+HPsFY="  # 客户端公钥(固定)
CLIENT_PresharedKey=""                                           # 预共享密钥(可为空)
CLIENT_AllowedIPs="********/32"                                  # 客户端允许的IP范围

# ============= 系统参数自动计算 =============

# IP地址智能分配函数 - 修复版：跳过.0和.255地址
calculate_ip_address() {
    local base_ip=$1      # 基础IP段,如 "172.16.0"
    local offset=$2       # IP偏移量(第几个命名空间)
    local start_offset=$3 # 起始偏移量,默认为10

    # 将基础IP按点分割为数组
    local ip_parts=($(echo $base_ip | tr '.' ' '))
    local ip_a=${ip_parts[0]} # 第一个八位字节
    local ip_b=${ip_parts[1]} # 第二个八位字节
    local ip_c=${ip_parts[2]} # 第三个八位字节

    # 计算总偏移量
    local total_offset=$((start_offset + offset))

    # 循环直到找到有效的IP地址(跳过.0和.255)
    while true; do
        # 计算最终的第3和第4个八位字节
        local final_c=$((ip_c + total_offset / 256))
        local final_d=$((total_offset % 256))

        # 检查IP范围是否超出有效范围
        if [ $final_c -gt 255 ]; then
            echo "ERROR: IP超出范围"
            return 1
        fi

        # 跳过网络地址(.0)和广播地址(.255)
        if [ $final_d -ne 0 ] && [ $final_d -ne 255 ]; then
            echo "$ip_a.$ip_b.$final_c.$final_d"
            return 0
        fi

        # 如果遇到.0或.255,偏移量+1继续寻找
        total_offset=$((total_offset + 1))
    done
}

# 子网掩码自动计算函数 - 根据命名空间数量智能选择掩码
calculate_subnet_mask() {
    local num=$1
    # 计算需要的IP数量：命名空间数量 + 网关 + 预留地址 + 跳过的.0/.255地址
    # 增加30%的余量来补偿跳过的地址
    local needed_ips=$((num * 130 / 100 + 50))

    # 计算需要的主机位数
    local bits=0
    local hosts=1
    while [ $hosts -lt $needed_ips ]; do
        bits=$((bits + 1))
        hosts=$((hosts * 2))
    done

    # 子网掩码 = 32 - 主机位数
    local mask=$((32 - bits))

    # 确保掩码在合理范围内 (最小/8,最大/24)
    if [ $mask -lt 8 ]; then
        mask=8
    elif [ $mask -gt 24 ]; then
        mask=24
    fi

    echo $mask
}

# 自动计算子网配置
SUBNET_MASK=$(calculate_subnet_mask $NUM_NAMESPACES)
SUBNET_BASE="${IPVLAN_IP}.0" # 使用配置的基础IP段
SUBNET="$SUBNET_BASE/$SUBNET_MASK"
GATEWAY_IP="${IPVLAN_IP}.1" # 网关IP使用基础IP段的.1
NS_IP_BASE="$IPVLAN_IP"     # 命名空间IP基础段

# 业务相关命名 - 基于业务名称统一命名
IPVLAN_HOST="${BUSINESS_NAME}-host"
IPVLAN_PREFIX="${BUSINESS_NAME}-ipvlan"
NS_PREFIX="${BUSINESS_NAME}-ns"
WG_CONFIG_BASE="/etc/netns"

# nftables相关变量 - 修改表名为fw_table
NFT_TABLE="${BUSINESS_NAME}_fw_table"
NFT_CHAIN_PREROUTING="${BUSINESS_NAME}_prerouting"
NFT_CHAIN_POSTROUTING="${BUSINESS_NAME}_postrouting"
NFT_CHAIN_FORWARD="${BUSINESS_NAME}_forward"
NFT_CHAIN_OUT="${BUSINESS_NAME}_out" # 新增OUTPUT链

# ============= 客户机访问控制规则模板 =============

# 默认访问控制规则模板 - iptables
declare -A ACCESS_CONTROL_RULES_DEFAULT_IPTABLES=(
    #["allow_vpn_subnet"]="OUTPUT -s $SUBNET -j ACCEPT"
)

# 默认访问控制规则模板 - nftables
declare -A ACCESS_CONTROL_RULES_DEFAULT_NFTABLES=(
    #["allow_vpn_subnet"]="ip saddr $SUBNET accept"
)

# 严格访问控制规则模板 - iptables !!!编号倒序排列!!!
declare -A ACCESS_CONTROL_RULES_STRICT_IPTABLES=(
    ["5"]="OUTPUT -s $SUBNET -p udp --sport $SERVER_ListenPort -j ACCEPT"
    ["4"]="OUTPUT -s $SUBNET -d *********** -p tcp --dport 443 -j ACCEPT"
    ["3"]="OUTPUT -s $SUBNET -d *************** -p icmp -j ACCEPT"
    ["2"]="OUTPUT -s $SUBNET -d *************** -p udp --dport 53 -j ACCEPT"
    ["1"]="OUTPUT -s $SUBNET -j LOG --log-prefix \"blocked-OUTPUT-\" --log-level 4"
)

# 严格访问控制规则模板 - nftables !!!编号倒序排列!!!
declare -A ACCESS_CONTROL_RULES_STRICT_NFTABLES=(
    ["4"]="ip saddr $SUBNET udp sport $SERVER_ListenPort accept"
    ["3"]="ip saddr $SUBNET ip daddr { *********** } tcp dport { 80, 443 } accept"
    ["2"]="ip saddr $SUBNET ip daddr { *************** } icmp type echo-request accept"
    ["1"]="ip saddr $SUBNET ip daddr { *************** } udp dport { 53 } ct state new accept"
    ["0"]="ip saddr $SUBNET counter log prefix \"blocked-OUTPUT-\" drop"
)

# ============= 动态优先级生成函数 =============

# 获取系统中已使用的优先级
get_used_priorities() {
    local hook_type=$1 # prerouting, postrouting, forward等

    # 获取指定hook类型的所有优先级
    nft list ruleset 2>/dev/null | grep -E "type .* hook $hook_type priority" |
        sed -E 's/.*priority ([+-]?[0-9]+).*/\1/' | sort -n | uniq
}

# 生成可用的优先级
generate_safe_priority() {
    local hook_type=$1
    local preferred=$2 # 期望的优先级

    local used_priorities=($(get_used_priorities $hook_type))

    # 如果期望优先级未被使用,直接返回
    for used in "${used_priorities[@]}"; do
        if [ "$used" = "$preferred" ]; then
            # 找一个可用的优先级(向后偏移)
            local offset=1
            while true; do
                local candidate=$((preferred + offset))
                local available=true
                for used in "${used_priorities[@]}"; do
                    if [ "$used" = "$candidate" ]; then
                        available=false
                        break
                    fi
                done
                if [ "$available" = true ]; then
                    echo $candidate
                    return
                fi
                offset=$((offset + 1))
                # 防止无限循环
                if [ $offset -gt 1000 ]; then
                    echo $((preferred + offset))
                    return
                fi
            done
        fi
    done

    # 期望优先级可用
    echo $preferred
}

# ============= 显示优化变量 =============
PROGRESS_LINE="" # 存储进度条行
STATUS_LINE=""   # 存储状态行
ERROR_COUNT=0    # 错误计数
SUCCESS_COUNT=0  # 成功计数

# ============= 脚本信息输出 =============
echo "=== IPVlan L3大规模命名空间隔离脚本 - 完善防火墙策略功能版 ==="
echo "业务名称: $BUSINESS_NAME"
echo "防火墙引擎: $FIREWALL_ENGINE"
echo "命名空间数量: $NUM_NAMESPACES"
echo "并发清理进程数: $MAX_PARALLEL"
echo "IP基础段: $IPVLAN_IP"
echo "自动子网: $SUBNET (掩码: /$SUBNET_MASK)"
echo "网关地址: $GATEWAY_IP"
echo "服务接口: $SERVICE_INTERFACE"
echo "服务IP: $SERVICE_IP"
echo "外访IP: $EXTERNAL_ACCESS_IP (统一NAT外访IP)"
echo "端口范围: $BASE_PORT - $((BASE_PORT + NUM_NAMESPACES - 1))"
echo "🔧 优化特性: 防火墙规则使用IP地址而非接口名,更精确稳定"

echo ""
echo "📊 网络层级架构："
echo "  1. 服务网络: $SERVICE_INTERFACE ($SERVICE_IP)"
echo "  2. 命名空间网络: IPVlan ($SUBNET)"
echo "  3. WireGuard网络: 10.0.0.x (命名空间内)"
echo ""
echo "🔄 NAT层级："
echo "  第1层: WireGuard(10.0.0.x) → 命名空间(172.16.0.x) [命名空间内NAT]"
echo "  第2层: 命名空间(172.16.0.x) → 外访IP($EXTERNAL_ACCESS_IP) → 外网 [主机NAT]"

echo ""
echo "🛡️  客户机访问控制配置："
echo "  访问控制模板: $ACCESS_CONTROL_TEMPLATE"

# ============= 外访IP验证 =============
echo ""
echo "🔍 服务IP和外访IP验证："
echo "  服务IP: $SERVICE_IP (用于客户端连接和防火墙规则)"
echo "  外访IP: $EXTERNAL_ACCESS_IP (统一NAT外访IP)"
echo "  防火墙规则: 基于IP地址而非接口名 ✅"

# 验证服务IP是否配置在接口上
if ip addr show | grep -q "$SERVICE_IP"; then
    echo "  服务IP已配置在本机接口上 ✅"
else
    echo "  ⚠️  警告: 服务IP未在本机接口上找到,请确保配置正确"
fi

# 验证外访IP是否配置在接口上(如果与服务IP不同)
if [ "$EXTERNAL_ACCESS_IP" != "$SERVICE_IP" ]; then
    if ip addr show | grep -q "$EXTERNAL_ACCESS_IP"; then
        echo "  外访IP已配置在本机接口上 ✅"
    else
        echo "  ⚠️  警告: 外访IP未在本机接口上找到,请确保路由配置正确"
    fi
fi

# ============= 参数验证 =============
# 检查命名空间数量范围
if [ $NUM_NAMESPACES -lt 1 ] || [ $NUM_NAMESPACES -gt 10000 ]; then
    echo "错误: 命名空间数量必须在1-10000之间"
    exit 1
fi

# 检查防火墙引擎选择
if [ "$FIREWALL_ENGINE" != "iptables" ] && [ "$FIREWALL_ENGINE" != "nftables" ]; then
    echo "错误: 防火墙引擎必须是 iptables 或 nftables"
    exit 1
fi

# 检查访问控制模板
if [ "$ACCESS_CONTROL_TEMPLATE" != "default" ] && [ "$ACCESS_CONTROL_TEMPLATE" != "strict" ] && [ "$ACCESS_CONTROL_TEMPLATE" != "custom" ]; then
    echo "错误: 访问控制模板必须是 default、strict 或 custom"
    exit 1
fi

# 检查运行权限
if [[ $EUID -ne 0 ]]; then
    echo "错误: 此脚本需要root权限运行"
    exit 1
fi

# 检查必需的系统工具
for tool in ip wg wg-quick; do
    if ! command -v $tool &>/dev/null; then
        echo "错误: 未找到 $tool 命令,请先安装 wireguard-tools"
        exit 1
    fi
done

# 检查防火墙工具可用性
if [ "$FIREWALL_ENGINE" = "iptables" ]; then
    if ! command -v iptables &>/dev/null; then
        echo "错误: 未找到 iptables 命令,请先安装 iptables"
        exit 1
    fi
elif [ "$FIREWALL_ENGINE" = "nftables" ]; then
    if ! command -v nft &>/dev/null; then
        echo "错误: 未找到 nft 命令,请先安装 nftables"
        exit 1
    fi
fi

# ============= 防火墙函数定义 =============

# iptables 相关函数 - 主机级NAT(第2层),使用IP地址而非接口名
fw_iptables_clean() {
    echo "清理 iptables 规则..."

    # 清理SNAT规则
    iptables -t nat -D POSTROUTING -s $SUBNET -j SNAT --to-source $EXTERNAL_ACCESS_IP 2>/dev/null || true
    # 兼容清理可能的接口基础规则
    iptables -t nat -D POSTROUTING -s $SUBNET -o $SERVICE_INTERFACE -j SNAT --to-source $EXTERNAL_ACCESS_IP 2>/dev/null || true
    iptables -t nat -D POSTROUTING -s $SUBNET -o $SERVICE_INTERFACE -j MASQUERADE 2>/dev/null || true

    # 清理端口转发规则
    iptables-save | grep -v "${BUSINESS_NAME}\|$BASE_PORT\|$SUBNET" | iptables-restore 2>/dev/null || true

    echo "✅ iptables规则清理完成"
}

fw_iptables_setup_nat() {
    echo "配置 iptables 主机级NAT规则 (第2层NAT: 命名空间→外访IP→外网,基于IP地址)..."
    echo "使用SNAT模式,固定外访IP: $EXTERNAL_ACCESS_IP"

    # 使用IP地址的SNAT规则,不依赖接口名
    iptables -t nat -A POSTROUTING -s $SUBNET -j SNAT --to-source $EXTERNAL_ACCESS_IP || {
        echo "错误: 无法添加SNAT规则"
        return 1
    }

    # 修改FORWARD规则 - 只允许VPN端口通过,其余丢弃
    iptables -A FORWARD -d $SUBNET -p udp --dport $SERVER_ListenPort -j ACCEPT 2>/dev/null || true
    iptables -A FORWARD -s 0.0.0.0/0 -d $SUBNET -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT 2>/dev/null || true
    iptables -A FORWARD -d $SUBNET -j DROP 2>/dev/null || true

    # 添加客户机访问控制规则
    fw_iptables_setup_access_control

    echo "✅ iptables 主机级NAT规则配置完成 (基于IP地址,外访IP: $EXTERNAL_ACCESS_IP)"
}

fw_iptables_setup_access_control() {
    echo "配置 iptables 客户机访问控制规则..."

    case "$ACCESS_CONTROL_TEMPLATE" in
    "default")
        for rule_name in "${!ACCESS_CONTROL_RULES_DEFAULT_IPTABLES[@]}"; do
            rule="${ACCESS_CONTROL_RULES_DEFAULT_IPTABLES[$rule_name]}"
            iptables -A $rule 2>/dev/null || true
        done
        ;;
    "strict")
        for rule_name in "${!ACCESS_CONTROL_RULES_STRICT_IPTABLES[@]}"; do
            rule="${ACCESS_CONTROL_RULES_STRICT_IPTABLES[$rule_name]}"
            iptables -A $rule 2>/dev/null || true
        done
        ;;
    "custom")
        echo "自定义访问控制规则 - 请在此处添加您的规则"
        ;;
    esac
}

fw_iptables_setup_port_forward() {
    local ns_ip=$1
    local map_port=$2

    # 使用目标IP地址而非接口名的端口转发规则
    iptables -t nat -A PREROUTING -p udp -d $SERVICE_IP --dport $map_port -j DNAT --to $ns_ip:$SERVER_ListenPort 2>/dev/null || {
        return 1
    }
}

# nftables 相关函数 - 主机级NAT(第2层),使用IP地址而非接口名,动态优先级
fw_nftables_clean() {
    echo "清理 nftables 规则..."

    # 删除整个业务表
    nft delete table ip $NFT_TABLE 2>/dev/null && echo "删除nftables表: $NFT_TABLE" || echo "nftables表不存在"

    echo "✅ nftables规则清理完成"
}

# nftables调试函数
nft_debug_cmd() {
    local cmd="$1"
    local description="$2"

    echo "执行: $description"
    echo "命令: $cmd"

    if eval "$cmd"; then
        echo "✅ $description 成功"
        return 0
    else
        echo "❌ $description 失败"
        return 1
    fi
}

fw_nftables_setup_nat() {
    echo "配置 nftables 主机级NAT规则 (第2层NAT: 命名空间→外访IP→外网,基于IP地址,动态优先级)..."
    echo "使用SNAT模式,固定外访IP: $EXTERNAL_ACCESS_IP"

    # 检查已存在的优先级以避免冲突
    echo "步骤1: 检查系统中已使用的优先级"
    echo "现有prerouting优先级: $(get_used_priorities prerouting | tr '\n' ' ')"
    echo "现有postrouting优先级: $(get_used_priorities postrouting | tr '\n' ' ')"
    echo "现有forward优先级: $(get_used_priorities forward | tr '\n' ' ')"
    echo "现有output优先级: $(get_used_priorities output | tr '\n' ' ')"

    # 动态生成安全的优先级
    PREROUTING_PRIORITY=$(generate_safe_priority prerouting -150)
    POSTROUTING_PRIORITY=$(generate_safe_priority postrouting 150)
    FORWARD_PRIORITY=$(generate_safe_priority forward 50)
    OUTPUT_PRIORITY=$(generate_safe_priority output 0)

    echo "使用动态优先级: PREROUTING=$PREROUTING_PRIORITY, POSTROUTING=$POSTROUTING_PRIORITY, FORWARD=$FORWARD_PRIORITY, OUTPUT=$OUTPUT_PRIORITY"

    # 1. 创建独立的业务表
    echo "步骤2: 创建独立的业务表"
    if ! nft_debug_cmd "nft add table ip $NFT_TABLE" "创建业务表 $NFT_TABLE"; then
        if nft list table ip $NFT_TABLE >/dev/null 2>&1; then
            echo "表已存在,清空后重新配置..."
            nft flush table ip $NFT_TABLE
        else
            echo "错误: 无法创建nftables表"
            return 1
        fi
    fi

    # 2. 创建链 - 使用动态优先级避免冲突
    echo "步骤3: 创建链(使用动态优先级)"

    # PREROUTING链 (用于DNAT) - 使用动态优先级
    if ! nft_debug_cmd "nft add chain ip $NFT_TABLE $NFT_CHAIN_PREROUTING '{ type nat hook prerouting priority $PREROUTING_PRIORITY; policy accept; }'" "创建PREROUTING链"; then
        if nft list chain ip $NFT_TABLE $NFT_CHAIN_PREROUTING >/dev/null 2>&1; then
            echo "PREROUTING链已存在"
        else
            echo "错误: 无法创建PREROUTING链"
            return 1
        fi
    fi

    # POSTROUTING链 (用于SNAT) - 使用动态优先级
    if ! nft_debug_cmd "nft add chain ip $NFT_TABLE $NFT_CHAIN_POSTROUTING '{ type nat hook postrouting priority $POSTROUTING_PRIORITY; policy accept; }'" "创建POSTROUTING链"; then
        if nft list chain ip $NFT_TABLE $NFT_CHAIN_POSTROUTING >/dev/null 2>&1; then
            echo "POSTROUTING链已存在"
        else
            echo "错误: 无法创建POSTROUTING链"
            return 1
        fi
    fi

    # FORWARD链 (用于过滤) - 使用动态优先级,修改策略
    if ! nft_debug_cmd "nft add chain ip $NFT_TABLE $NFT_CHAIN_FORWARD '{ type filter hook forward priority $FORWARD_PRIORITY; policy accept; }'" "创建FORWARD链"; then
        if nft list chain ip $NFT_TABLE $NFT_CHAIN_FORWARD >/dev/null 2>&1; then
            echo "FORWARD链已存在"
        else
            echo "错误: 无法创建FORWARD链"
            return 1
        fi
    fi

    # OUTPUT链 (用于客户机访问控制) - 使用动态优先级
    if ! nft_debug_cmd "nft add chain ip $NFT_TABLE $NFT_CHAIN_OUT '{ type filter hook output priority $OUTPUT_PRIORITY; policy accept; }'" "创建OUTPUT链"; then
        if nft list chain ip $NFT_TABLE $NFT_CHAIN_OUT >/dev/null 2>&1; then
            echo "OUTPUT链已存在"
        else
            echo "错误: 无法创建OUTPUT链"
            return 1
        fi
    fi

    # 3. 验证链创建成功
    echo "步骤4: 验证链创建"
    for chain in $NFT_CHAIN_PREROUTING $NFT_CHAIN_POSTROUTING $NFT_CHAIN_FORWARD $NFT_CHAIN_OUT; do
        if ! nft list chain ip $NFT_TABLE $chain >/dev/null 2>&1; then
            echo "错误: 链 $chain 创建失败"
            return 1
        else
            echo "✅ 链 $chain 验证成功"
        fi
    done

    # 4. 添加主机级SNAT规则 - 基于IP地址而非接口名
    echo "步骤5: 添加主机级SNAT规则(第2层NAT,基于IP地址,外访IP: $EXTERNAL_ACCESS_IP)"

    if ! nft_debug_cmd "nft add rule ip $NFT_TABLE $NFT_CHAIN_POSTROUTING ip saddr $SUBNET snat to $EXTERNAL_ACCESS_IP" "添加基于IP地址的SNAT规则"; then
        echo "错误: 无法添加SNAT规则"
        return 1
    fi

    # 5. 添加FORWARD规则 - 基于IP地址,修改为只允许VPN端口
    echo "步骤6: 添加FORWARD规则(基于IP地址,只允许VPN端口)"

    # 只允许目的为VPN子网且UDP端口为VPN监听端口的流量
    nft_debug_cmd "nft add rule ip $NFT_TABLE $NFT_CHAIN_FORWARD ip daddr $SUBNET udp dport $SERVER_ListenPort accept" "允许VPN端口转发"

    # 允许外网到命名空间的返回流量 - 基于目标IP
    nft_debug_cmd "nft add rule ip $NFT_TABLE $NFT_CHAIN_FORWARD ip daddr $SUBNET ct state related,established accept" "外网返回流量(基于IP)"

    # 其他目的为VPN子网的流量一律丢弃
    nft_debug_cmd "nft add rule ip $NFT_TABLE $NFT_CHAIN_FORWARD ip daddr $SUBNET log prefix \"blocked-FORWARD-\" drop" "丢弃其他VPN子网流量"

    # 6. 添加客户机访问控制规则
    echo "步骤7: 添加客户机访问控制规则"
    fw_nftables_setup_access_control

    echo "✅ nftables 主机级NAT规则配置完成 (基于IP地址,外访IP: $EXTERNAL_ACCESS_IP)"
}

fw_nftables_setup_access_control() {
    echo "配置 nftables 客户机访问控制规则..."

    case "$ACCESS_CONTROL_TEMPLATE" in
    "default")
        for rule_name in "${!ACCESS_CONTROL_RULES_DEFAULT_NFTABLES[@]}"; do
            rule="${ACCESS_CONTROL_RULES_DEFAULT_NFTABLES[$rule_name]}"
            nft_debug_cmd "nft add rule ip $NFT_TABLE $NFT_CHAIN_OUT $rule" "添加默认规则: $rule_name"
        done
        ;;
    "strict")
        for rule_name in "${!ACCESS_CONTROL_RULES_STRICT_NFTABLES[@]}"; do
            rule="${ACCESS_CONTROL_RULES_STRICT_NFTABLES[$rule_name]}"
            nft_debug_cmd "nft add rule ip $NFT_TABLE $NFT_CHAIN_OUT $rule" "添加严格规则: $rule_name"
        done
        ;;
    "custom")
        echo "自定义访问控制规则 - 请在此处添加您的规则"
        ;;
    esac
}

fw_nftables_setup_port_forward() {
    local ns_ip=$1
    local map_port=$2

    # 验证链是否存在
    if ! nft list chain ip $NFT_TABLE $NFT_CHAIN_PREROUTING >/dev/null 2>&1; then
        echo "错误: PREROUTING链不存在"
        return 1
    fi

    # 添加基于IP地址的端口转发规则
    if nft add rule ip $NFT_TABLE $NFT_CHAIN_PREROUTING ip daddr $SERVICE_IP udp dport $map_port dnat to $ns_ip:$SERVER_ListenPort 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 统一防火墙接口函数
fw_clean() {
    if [ "$FIREWALL_ENGINE" = "iptables" ]; then
        fw_iptables_clean
    elif [ "$FIREWALL_ENGINE" = "nftables" ]; then
        fw_nftables_clean
    fi
}

fw_setup_nat() {
    if [ "$FIREWALL_ENGINE" = "iptables" ]; then
        fw_iptables_setup_nat
    elif [ "$FIREWALL_ENGINE" = "nftables" ]; then
        fw_nftables_setup_nat
    fi
}

fw_setup_port_forward() {
    local ns_ip=$1
    local map_port=$2

    if [ "$FIREWALL_ENGINE" = "iptables" ]; then
        fw_iptables_setup_port_forward $ns_ip $map_port
    elif [ "$FIREWALL_ENGINE" = "nftables" ]; then
        fw_nftables_setup_port_forward $ns_ip $map_port
    fi
}

# 生成WireGuard配置文件的防火墙规则 - 第1层NAT(命名空间内)
generate_wg_firewall_rules() {
    local ipvlan_if=$1

    if [ "$FIREWALL_ENGINE" = "iptables" ]; then
        cat <<EOF
# 第1层NAT规则 - iptables(命名空间内：WireGuard → 命名空间网络)
PostUp = iptables -t nat -A POSTROUTING -s $SERVER_Address -o $ipvlan_if -j MASQUERADE 2>/dev/null || true
PostUp = iptables -A FORWARD -i wg0 -o $ipvlan_if -j ACCEPT 2>/dev/null || true
PostUp = iptables -A FORWARD -i $ipvlan_if -o wg0 -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT 2>/dev/null || true

# 清理规则
PostDown = iptables -t nat -D POSTROUTING -s $SERVER_Address -o $ipvlan_if -j MASQUERADE 2>/dev/null || true
PostDown = iptables -D FORWARD -i wg0 -o $ipvlan_if -j ACCEPT 2>/dev/null || true
PostDown = iptables -D FORWARD -i $ipvlan_if -o wg0 -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT 2>/dev/null || true
EOF
    elif [ "$FIREWALL_ENGINE" = "nftables" ]; then
        cat <<EOF
# 第1层NAT规则 - nftables(命名空间内：WireGuard → 命名空间网络)
PostUp = nft add table ip wg_nat 2>/dev/null || true
PostUp = nft add chain ip wg_nat postrouting '{ type nat hook postrouting priority 100; policy accept; }' 2>/dev/null || true
PostUp = nft add chain ip wg_nat forward '{ type filter hook forward priority 0; policy accept; }' 2>/dev/null || true
PostUp = nft add rule ip wg_nat postrouting ip saddr $SERVER_Address oifname "$ipvlan_if" masquerade 2>/dev/null || true
PostUp = nft add rule ip wg_nat forward iifname wg0 oifname "$ipvlan_if" accept 2>/dev/null || true
PostUp = nft add rule ip wg_nat forward iifname "$ipvlan_if" oifname wg0 ct state related,established accept 2>/dev/null || true

# 清理规则
PostDown = nft delete table ip wg_nat 2>/dev/null || true
EOF
    fi
}

# ============= 并发清理函数 =============

# 单个命名空间清理函数
cleanup_single_namespace() {
    local ns_name=$1

    # 停止WireGuard服务
    ip netns exec $ns_name wg-quick down wg0 2>/dev/null || true

    # 删除命名空间
    ip netns del $ns_name 2>/dev/null || true

    echo "已清理命名空间: $ns_name"
}

# 并发清理命名空间函数
cleanup_namespaces_parallel() {
    echo "开始并发清理 $BUSINESS_NAME 相关命名空间..."

    # 获取现有的业务相关命名空间列表
    EXISTING_NS=$(ip netns list 2>/dev/null | grep "^${NS_PREFIX}" | awk '{print $1}')

    if [ -n "$EXISTING_NS" ]; then
        echo "发现 $(echo "$EXISTING_NS" | wc -l) 个命名空间需要清理"

        # 导出函数以便在子进程中使用
        export -f cleanup_single_namespace

        # 使用xargs并发处理,限制最大并发数
        echo "$EXISTING_NS" | xargs -n 1 -P $MAX_PARALLEL -I {} bash -c 'cleanup_single_namespace "$@"' _ {}

        echo "✅ 并发清理命名空间完成"
    else
        echo "未找到 $BUSINESS_NAME 相关命名空间"
    fi
}

# ============= 优化显示函数 =============

# 清除状态行
clear_status_line() {
    printf "\r%80s\r" " " # 清除当前行
}

# 显示进度和状态的组合函数
show_progress_with_status() {
    local current=$1
    local total=$2
    local status_msg="$3"
    local percent=$((current * 100 / total))
    local bar_length=50
    local filled_length=$((percent * bar_length / 100))

    # 构建进度条
    local progress_bar="["
    for ((i = 0; i < filled_length; i++)); do progress_bar+="█"; done
    for ((i = filled_length; i < bar_length; i++)); do progress_bar+="░"; done
    progress_bar+="]"

    # 原地刷新显示进度和状态
    printf "\r进度: %s %d%% (%d/%d) | %s" "$progress_bar" $percent $current $total "$status_msg"
}

# 显示错误信息但不打断进度显示
show_inline_error() {
    local error_msg="$1"
    clear_status_line
    echo "❌ $error_msg"
    ERROR_COUNT=$((ERROR_COUNT + 1))
}

# 显示成功信息但不打断进度显示
show_inline_success() {
    local success_msg="$1"
    SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    # 成功信息不显示,只更新计数
}

# ============= IP地址验证函数 =============
validate_ip_assignments() {
    echo ""
    echo "🔍 IP地址分配验证 (检查是否跳过.0/.255):"

    local sample_count=10
    if [ $NUM_NAMESPACES -lt 10 ]; then
        sample_count=$NUM_NAMESPACES
    fi

    echo "检查前 $sample_count 个IP分配..."
    for i in $(seq 1 $sample_count); do
        local test_ip=$(calculate_ip_address "$NS_IP_BASE" $i $IPVLAN_START_OFFSET)
        local last_octet=$(echo $test_ip | cut -d'.' -f4)

        if [ "$last_octet" = "0" ] || [ "$last_octet" = "255" ]; then
            echo "  ❌ 第 $i 个: $test_ip (包含无效地址)"
        else
            echo "  ✅ 第 $i 个: $test_ip"
        fi
    done
}

# 防火墙规则状态显示函数
fw_show_rules() {
    echo ""
    echo "🔥 防火墙规则状态 (完善防火墙策略,基于IP地址,外访IP: $EXTERNAL_ACCESS_IP):"

    if [ "$FIREWALL_ENGINE" = "iptables" ]; then
        echo "iptables 主机级NAT规则数量:"
        NAT_PREROUTING=$(iptables -t nat -L PREROUTING -n 2>/dev/null | grep -c "dpt:$BASE_PORT\|$BASE_PORT" || echo 0)
        NAT_POSTROUTING=$(iptables -t nat -L POSTROUTING -n 2>/dev/null | grep -c "$SUBNET\|$EXTERNAL_ACCESS_IP" || echo 0)
        FORWARD_RULES=$(iptables -L FORWARD -n 2>/dev/null | grep -c "$SUBNET" || echo 0)
        OUTPUT_RULES=$(iptables -L OUTPUT -n 2>/dev/null | grep -c "$SUBNET" || echo 0)
        echo "  PREROUTING (端口转发): $NAT_PREROUTING"
        echo "  POSTROUTING (主机SNAT): $NAT_POSTROUTING"
        echo "  FORWARD (转发规则): $FORWARD_RULES"
        echo "  OUTPUT (访问控制): $OUTPUT_RULES"

        # 显示SNAT规则详情
        echo "  SNAT规则详情:"
        iptables -t nat -L POSTROUTING -n 2>/dev/null | grep "$SUBNET.*$EXTERNAL_ACCESS_IP" && echo "    使用SNAT到$EXTERNAL_ACCESS_IP (基于IP地址) ✅" || echo "    SNAT规则缺失 ❌"

    elif [ "$FIREWALL_ENGINE" = "nftables" ]; then
        echo "nftables 主机级防火墙表状态:"
        if nft list table ip $NFT_TABLE >/dev/null 2>&1; then
            echo "  业务表名: $NFT_TABLE ✅"

            # 检查链状态
            for chain in $NFT_CHAIN_PREROUTING $NFT_CHAIN_POSTROUTING $NFT_CHAIN_FORWARD $NFT_CHAIN_OUT; do
                if nft list chain ip $NFT_TABLE $chain >/dev/null 2>&1; then
                    CHAIN_RULES=$(nft list chain ip $NFT_TABLE $chain 2>/dev/null | grep -c "rule " || echo 0)
                    echo "  链 $chain: 存在 (规则数: $CHAIN_RULES)"
                else
                    echo "  链 $chain: 不存在 ❌"
                fi
            done

            # 显示详细的规则信息
            echo "  PREROUTING规则数量: $(nft list chain ip $NFT_TABLE $NFT_CHAIN_PREROUTING 2>/dev/null | grep -c "dnat to" || echo 0)"
            echo "  POSTROUTING规则数量: $(nft list chain ip $NFT_TABLE $NFT_CHAIN_POSTROUTING 2>/dev/null | grep -c "snat to" || echo 0)"
            echo "  FORWARD规则数量: $(nft list chain ip $NFT_TABLE $NFT_CHAIN_FORWARD 2>/dev/null | grep -c "accept\|drop" || echo 0)"
            echo "  OUTPUT规则数量: $(nft list chain ip $NFT_TABLE $NFT_CHAIN_OUT 2>/dev/null | grep -c "accept\|drop" || echo 0)"

            # 显示SNAT规则详情
            echo "  SNAT规则详情:"
            nft list chain ip $NFT_TABLE $NFT_CHAIN_POSTROUTING 2>/dev/null | grep -q "snat to $EXTERNAL_ACCESS_IP" && echo "    使用SNAT到$EXTERNAL_ACCESS_IP (基于IP地址) ✅" || echo "    SNAT规则缺失 ❌"

            # 显示FORWARD规则策略
            echo "  FORWARD规则策略:"
            nft list chain ip $NFT_TABLE $NFT_CHAIN_FORWARD 2>/dev/null | grep -q "udp dport $SERVER_ListenPort accept" && echo "    只允许VPN端口通过 ✅" || echo "    VPN端口规则缺失 ❌"
            nft list chain ip $NFT_TABLE $NFT_CHAIN_FORWARD 2>/dev/null | grep -q "ip daddr $SUBNET log prefix \"blocked-FORWARD-\" drop" && echo "    其余流量丢弃 ✅" || echo "    丢弃规则缺失 ❌"

            # 显示访问控制策略
            echo "  客户机访问控制策略:"
            echo "    模板: $ACCESS_CONTROL_TEMPLATE"
            if [ "$ACCESS_CONTROL_TEMPLATE" = "strict" ]; then
                nft list chain ip $NFT_TABLE $NFT_CHAIN_OUT 2>/dev/null | grep -q "tcp dport.*80.*443" && echo "    Web访问控制 ✅" || echo "    Web访问控制 ❌"
                nft list chain ip $NFT_TABLE $NFT_CHAIN_OUT 2>/dev/null | grep -q "udp dport.*53" && echo "    DNS访问控制 ✅" || echo "    DNS访问控制 ❌"
                nft list chain ip $NFT_TABLE $NFT_CHAIN_OUT 2>/dev/null | grep -q "icmp type echo-request" && echo "    Ping访问控制 ✅" || echo "    Ping访问控制 ❌"
            fi

            # 显示使用的优先级
            echo "  使用的优先级:"
            nft list table ip $NFT_TABLE 2>/dev/null | grep -E "type .* hook .* priority" |
                sed -E 's/.*type (.*) hook (.*) priority ([+-]?[0-9]+).*/    \2: \3/' || true

            # 显示规则使用IP地址而非接口名的验证
            echo "  IP地址规则验证:"
            local ip_rules=$(nft list table ip $NFT_TABLE 2>/dev/null | grep -c "ip saddr\|ip daddr" || echo 0)
            local iif_rules=$(nft list table ip $NFT_TABLE 2>/dev/null | grep -c "iifname\|oifname" || echo 0)
            echo "    基于IP地址的规则: $ip_rules"
            echo "    基于接口名的规则: $iif_rules"
            if [ $ip_rules -gt 0 ] && [ $iif_rules -eq 0 ]; then
                echo "    ✅ 规则正确使用IP地址"
            else
                echo "    ⚠️  规则混合使用IP地址和接口名"
            fi
        else
            echo "  业务表状态: 不存在 ❌"
        fi

        echo ""
        echo "系统中其他nftables表:"
        nft list tables 2>/dev/null | grep -v "$NFT_TABLE" | while read table; do
            echo "  $table"
        done
    fi

    echo ""
    echo "🔍 命名空间内NAT状态 (抽样检查前3个):"
    local check_count=$NUM_NAMESPACES
    if [ $check_count -gt 3 ]; then
        check_count=3
    fi

    for i in $(seq 1 $check_count); do
        NS_NAME="${NS_PREFIX}$i"
        echo -n "  $NS_NAME WireGuard NAT: "

        if [ "$FIREWALL_ENGINE" = "iptables" ]; then
            if ip netns exec $NS_NAME iptables -t nat -L POSTROUTING -n 2>/dev/null | grep -q "$SERVER_Address"; then
                echo "✅ (iptables)"
            else
                echo "❌ (iptables)"
            fi
        elif [ "$FIREWALL_ENGINE" = "nftables" ]; then
            if ip netns exec $NS_NAME nft list table ip wg_nat >/dev/null 2>&1; then
                echo "✅ (nftables)"
            else
                echo "❌ (nftables)"
            fi
        fi
    done
}

# ============= 资源清理阶段 =============
echo ""
echo "=== 快速清理现有 $BUSINESS_NAME 相关资源 ==="

# 1. 并发清理命名空间
cleanup_namespaces_parallel

# 2. 清理IPVlan设备 - 只清理业务相关的
echo "清理 $BUSINESS_NAME 相关IPVlan设备..."
# 清理主机网关设备
ip link del $IPVLAN_HOST 2>/dev/null && echo "删除: $IPVLAN_HOST" || true

# 清理业务相关的ipvlan设备
EXISTING_IPVLAN=$(ip link show 2>/dev/null | grep "^[0-9]*: ${IPVLAN_PREFIX}" | cut -d: -f2 | tr -d ' ')
if [ -n "$EXISTING_IPVLAN" ]; then
    echo "$EXISTING_IPVLAN" | while read dev; do
        echo "删除IPVlan设备: $dev"
        ip link del $dev 2>/dev/null || true
    done
else
    echo "未找到 $BUSINESS_NAME 相关IPVlan设备"
fi

# 3. 清理路由 - 只清理业务相关的
echo "清理 $BUSINESS_NAME 相关路由..."
ip route del $SUBNET 2>/dev/null && echo "删除路由: $SUBNET" || echo "路由不存在"

# 4. 清理防火墙规则 - 使用统一接口
fw_clean

# 5. 清理配置目录 - 只清理业务相关的
echo "清理 $BUSINESS_NAME 相关配置目录..."
if [ -d "$WG_CONFIG_BASE" ]; then
    find $WG_CONFIG_BASE -maxdepth 1 -type d -name "${NS_PREFIX}*" -exec rm -rf {} \; 2>/dev/null && echo "删除配置目录完成" || echo "无配置目录需要清理"
else
    echo "配置基础目录不存在"
fi

echo "✅ 资源清理完成"

# ============= IP地址分配验证 =============
validate_ip_assignments

# ============= 系统配置阶段 =============

# 启用IP转发
echo ""
echo "启用IP转发..."
sysctl -w net.ipv4.ip_forward=1 >/dev/null

# 创建主机侧网关接口
echo ""
echo "创建主机侧IPVlan网关接口..."
ip link add $IPVLAN_HOST link $SERVICE_INTERFACE type ipvlan mode l3

# 添加网关地址
ip addr add $GATEWAY_IP/$SUBNET_MASK dev $IPVLAN_HOST
ip link set $IPVLAN_HOST up

echo "✅ 主机网关接口创建成功: $IPVLAN_HOST ($GATEWAY_IP/$SUBNET_MASK)"

# 配置子网路由
echo ""
echo "配置子网路由..."
ip route add $SUBNET dev $IPVLAN_HOST 2>/dev/null || echo "路由已存在"

echo "✅ 子网路由配置完成: $SUBNET via $IPVLAN_HOST"

# 配置主机NAT - 第2层NAT,基于IP地址
echo ""
echo "配置主机NAT规则 (使用 $FIREWALL_ENGINE,第2层NAT: 命名空间→外访IP($EXTERNAL_ACCESS_IP)→外网,基于IP地址)..."
if fw_setup_nat; then
    echo "✅ 主机级NAT规则配置完成 (基于IP地址,外访IP: $EXTERNAL_ACCESS_IP)"
else
    echo "❌ 主机级NAT规则配置失败,脚本将继续运行但可能影响网络连通性"
    exit 1
fi

# ============= 批量创建命名空间阶段 =============
echo ""
echo "=== 批量创建 $NUM_NAMESPACES 个命名空间 ==="
echo "提示: 使用多层级NAT架构,完善的防火墙策略,基于IP地址而非接口名"

# 重置计数器
ERROR_COUNT=0
SUCCESS_COUNT=0

# 批量创建命名空间
for i in $(seq 1 $NUM_NAMESPACES); do
    NS_NAME="${NS_PREFIX}$i"
    IPVLAN_IF="${IPVLAN_PREFIX}$i"

    # 使用修复的IP分配函数,自动跳过.0和.255
    NS_IP=$(calculate_ip_address "$NS_IP_BASE" $i $IPVLAN_START_OFFSET)
    if [ "$NS_IP" = "ERROR: IP超出范围" ]; then
        clear_status_line
        echo ""
        echo "错误: 第 $i 个命名空间的IP地址超出范围,请减少命名空间数量或调整子网配置"
        break
    fi

    # 外部端口映射
    MAP_PORT=$((BASE_PORT + i - 1))

    # 显示当前操作状态
    show_progress_with_status $i $NUM_NAMESPACES "创建 $NS_NAME ($NS_IP:$MAP_PORT)"

    # 创建命名空间
    ip netns add $NS_NAME 2>/dev/null

    # 创建ipvlan接口并移至命名空间
    ip link add $IPVLAN_IF link $SERVICE_INTERFACE type ipvlan mode l3 2>/dev/null
    ip link set $IPVLAN_IF netns $NS_NAME 2>/dev/null

    # 配置命名空间内的网络
    ip netns exec $NS_NAME ip addr add $NS_IP/$SUBNET_MASK dev $IPVLAN_IF 2>/dev/null
    ip netns exec $NS_NAME ip link set lo up
    ip netns exec $NS_NAME ip link set $IPVLAN_IF up

    # 添加默认路由
    ip netns exec $NS_NAME ip route add default via $GATEWAY_IP dev $IPVLAN_IF 2>/dev/null

    # 创建WireGuard配置目录
    mkdir -p $WG_CONFIG_BASE/$NS_NAME/wireguard/

    # 创建WireGuard配置文件 - 包含第1层NAT规则(命名空间内)
    cat >$WG_CONFIG_BASE/$NS_NAME/wireguard/wg0.conf <<WGEOF
[Interface]
PrivateKey = $SERVER_PrivateKey
Address = $SERVER_Address
ListenPort = $SERVER_ListenPort

# 启用IP转发
PostUp = sysctl -w net.ipv4.ip_forward=1

$(generate_wg_firewall_rules $IPVLAN_IF)

[Peer]
PublicKey = $CLIENT_PublicKey
AllowedIPs = $CLIENT_AllowedIPs
WGEOF

    # 如果有预共享密钥,添加到配置中
    if [ -n "$CLIENT_PresharedKey" ]; then
        sed -i '/AllowedIPs = /a PresharedKey = '"$CLIENT_PresharedKey" $WG_CONFIG_BASE/$NS_NAME/wireguard/wg0.conf
    fi

    # 启动WireGuard (静默处理)
    if ip netns exec $NS_NAME bash -c "cd $WG_CONFIG_BASE/$NS_NAME/wireguard && wg-quick up wg0" >/dev/null 2>&1; then
        show_inline_success "WireGuard启动成功: $NS_NAME"
    else
        show_inline_error "WireGuard启动失败: $NS_NAME"
    fi

    # 配置端口转发 - 静默处理
    if fw_setup_port_forward $NS_IP $MAP_PORT; then
        show_inline_success "端口转发成功: $MAP_PORT -> $NS_IP:$SERVER_ListenPort"
    else
        show_inline_error "端口转发失败: $MAP_PORT -> $NS_IP:$SERVER_ListenPort"
    fi
done

# 完成创建,显示最终状态
clear_status_line
echo ""
echo "✅ $NUM_NAMESPACES 个命名空间创建完成 (成功: $SUCCESS_COUNT, 错误: $ERROR_COUNT)"

# 等待短暂时间,确保所有命名空间完全初始化
sleep 2

# ============= 显示最终状态 =============
echo ""
echo "=== 脚本执行完成 ==="

# 显示防火墙规则状态
fw_show_rules

echo ""
echo "🎉 IPVlan L3大规模命名空间隔离脚本执行完成！"
echo ""
echo "📝 主要功能总结："
echo "  ✅ 创建了 $NUM_NAMESPACES 个隔离的命名空间"
echo "  ✅ 配置了多层级NAT架构"
echo "  ✅ 部署了完善的防火墙策略 (使用 $FIREWALL_ENGINE)"
echo "  ✅ 启用了客户机访问控制 (模板: $ACCESS_CONTROL_TEMPLATE)"
echo "  ✅ 所有规则基于IP地址,稳定可靠"
echo ""
echo "🔗 连接信息："
echo "  服务IP: $SERVICE_IP"
echo "  端口范围: $BASE_PORT - $((BASE_PORT + NUM_NAMESPACES - 1))"
echo "  VPN子网: $SUBNET"
echo "  外访IP: $EXTERNAL_ACCESS_IP"
echo ""
echo "🛠️  管理命令："
echo "  查看命名空间: ip netns list | grep $NS_PREFIX"
echo "  进入命名空间: ip netns exec ${NS_PREFIX}1 bash"
echo "  查看WireGuard状态: ip netns exec ${NS_PREFIX}1 wg show"
echo "  查看防火墙规则: nft list table ip $NFT_TABLE"
